'==============================================================================
' 主函数：Dong_自动排版
' 功能：公文自动排版的主入口函数
' 作者：Dong
' 说明：按照公文格式标准自动排版Word文档
'==============================================================================
Sub Dong_自动排版()
'公文自动排版主程序
    Initial   '步骤1：初始化文档设置
    GWStyle  '步骤2：应用公文样式格式
    Title1   '步骤3：设置一级标题格式
    Inscribe '步骤4：处理落款和附件
    PageNumGW   '步骤5：设置公文页码
    Common '步骤6：公共部分调整和收尾工作
End Sub

'==============================================================================
' 函数：Initial
' 功能：初始化文档设置，为排版做准备
' 说明：设置页面参数、清理文档格式、处理表格等
'==============================================================================
Function Initial()
'文档初始化函数
    Dim t As Table  '定义表格变量，用于遍历文档中的表格

    '调用页面设置函数，设置为A4纸张大小和边距
    PaperSetup

    '设置页面显示模式为适合页面宽度，避免页面刷新问题
    ActiveWindow.ActivePane.View.Zoom.PageFit = wdPageFitBestFit

    '对当前活动文档进行初始化操作
    With ActiveDocument
        '从附加的模板中复制内置样式到当前文档，确保样式一致性
        .CopyStylesFromTemplate Template:=.AttachedTemplate.FullName

        '删除文档中的所有域（如日期、页码等自动更新字段），转为静态文本
        .Fields.Unlink

        '将文档中的自动编号列表转换为普通文本，便于后续处理
        .ConvertNumbersToText

        '使用查找替换功能清理文档格式
        With .Content.Find
            '将手动换行符(^l)替换为段落标记(^p)，统一换行格式
            .Execute "^l", , , 0, , , , , , "^p", 2
            '删除标点符号后的多余空格，保持格式整洁
            .Execute "([、.．）])([ 　^s^t]{1,})", , , 1, , , , , , "\1", 2
            '将"附"后面的内容标准化为"附件"格式
            .Execute "(^13附)([!一-﨩])", , , 1, , , , , , "\1件\2", 2
            '将"附表"统一替换为"附件"
            .Execute "附表", , , , , , , , , "附件", 2
        End With

        '处理文档中的表格格式
        For Each t In .Tables  '遍历文档中的所有表格
            With t.Range.Rows  '对表格的所有行进行设置
                .WrapAroundText = False  '取消文字环绕，表格不被文字环绕
                .Alignment = wdAlignRowCenter  '设置表格在页面中居中对齐
            End With
        Next
    End With
End Function

'==============================================================================
' 函数：PaperSetup
' 功能：设置文档的页面参数
' 说明：根据纸张方向设置不同的边距和页面尺寸
'==============================================================================
Function PaperSetup()
'页面设置函数
    Dim Sec As Section  '定义节变量，用于遍历文档的所有节
    
    '遍历文档中的所有节（一般文档只有一个节）
    For Each Sec In ActiveDocument.Sections
        With Sec.PageSetup  '设置当前节的页面参数
            '判断纸张方向：竖向（纵向）
            If .Orientation = wdOrientPortrait Then
                '竖向A4纸张设置（210mm x 297mm）
                .TopMargin = CentimetersToPoints(2.54)  '上边距：2.54厘米
                .BottomMargin = CentimetersToPoints(2.54)  '下边距：2.54厘米
                .LeftMargin = CentimetersToPoints(2.8)  '左边距：2.8厘米
                .RightMargin = CentimetersToPoints(2.6)  '右边距：2.6厘米
                .PageWidth = CentimetersToPoints(21)   '页面宽度：21厘米
                .PageHeight = CentimetersToPoints(29.7)  '页面高度：29.7厘米
            Else
                '横向纸张设置（297mm x 210mm）
                .TopMargin = CentimetersToPoints(2.5)  '上边距：2.5厘米
                .BottomMargin = CentimetersToPoints(2.5)  '下边距：2.5厘米
                .LeftMargin = CentimetersToPoints(2.54)  '左边距：2.54厘米
                .RightMargin = CentimetersToPoints(2.54)  '右边距：2.54厘米
                .PageWidth = CentimetersToPoints(29.7)  '页面宽度：29.7厘米
                .PageHeight = CentimetersToPoints(21)  '页面高度：21厘米
            End If
            '设置页脚距离页面底部的距离：1.75厘米
            .FooterDistance = CentimetersToPoints(1.75)
        End With
    Next
End Function

'==============================================================================
' 函数：GWStyle
' 功能：应用公文样式格式
' 说明：设置正文和各级标题的字体、段落格式，处理自动编号
'==============================================================================
Function GWStyle()
'公文样式设置函数
    '定义变量
    Dim doc As Document  '文档对象
    Dim i As Paragraph  '段落对象，用于遍历
    Dim r()  '范围数组，存储非表格区域
    Dim n As Long  '循环计数器
    Dim t2&, t3&, t4&, t5&  '各级标题的编号计数器

    '获取当前活动文档的引用
    Set doc = ActiveDocument

    '分离表格和文本区域，避免对表格内容进行样式处理
    With doc
        '根据表格数量初始化范围数组大小
        ReDim r(.Tables.Count + 1)

        '如果文档中没有表格
        If .Tables.Count = 0 Then
            '将整个文档内容作为一个范围
            Set r(1) = .Content
        Else
            '如果有表格，则分别获取表格之间的文本区域
            For n = 1 To .Tables.Count
                If n = 1 Then
                    '第一个区域：文档开始到第一个表格之前
                    Set r(n) = .Range(0, .Tables(n).Range.Start)
                Else
                    '中间区域：上一个表格结束到当前表格开始
                    Set r(n) = .Range(.Tables(n - 1).Range.End, .Tables(n).Range.Start)
                End If
            Next
            '最后一个区域：最后一个表格结束到文档结束
            Set r(n) = .Range(.Tables(n - 1).Range.End, .Content.End)
        End If
    End With

    '遍历所有非表格区域，对每个区域应用公文样式
    For n = 1 To UBound(r)
        With r(n)  '对当前区域进行处理
            .Select  '选中当前区域

            '执行删除段落首尾空格的命令（ID:122是Word内置命令）
            CommandBars.FindControl(ID:=122).Execute

            '清除当前选中区域的所有格式，重新开始设置
            Selection.ClearFormatting

            '设置正文的字体样式
            With .Font
                .NameFarEast = "方正仿宋_GBK"  '中文字体：方正仿宋
                .NameAscii = "宋体"  '英文字体：宋体
                .Size = 16  '字体大小：16磅（3号字体）
                .Color = wdColorBlue  '字体颜色：蓝色（临时标记，最后会恢复为黑色）
                .Kerning = 0  '字符间距：0
                .DisableCharacterSpaceGrid = True  '禁用字符网格
            End With
            '设置正文的段落格式
            With .ParagraphFormat
                .LineSpacingRule = wdLineSpaceExactly  '行距规则：固定值
                .LineSpacing = 29  '行距：29磅（公文标准）
                .CharacterUnitFirstLineIndent = 2  '首行缩进：2个字符
                .AutoAdjustRightIndent = False  '不自动调整右缩进
                .DisableLineHeightGrid = True  '禁用行高网格
            End With

            '如果不是文档开始位置，在前面插入一个段落
            If .Start <> 0 Then .InsertParagraphBefore

            '使用查找替换功能标准化编号格式
            With .Find
                '将中文数字编号统一为"一、"格式
                .Execute "(^13)([一二三四五六七八九十百零〇○Oo0０Ｏｏ]@)(、)", , , 1, , , , , , "\1一\3", 2
                '将括号中文数字统一为"（一）"格式
                .Execute "(^13)([(（][一二三四五六七八九十百零〇○Oo0０Ｏｏ]@[）)])", , , 1, , , , , , "\1（一）", 2
                '将阿拉伯数字编号统一为"1．"格式
                .Execute "(^13)([0-9０-９]@[、.．])", , , 1, , , , , , "\11．", 2
                '将括号阿拉伯数字统一为"（1）"格式
                .Execute "(^13)[(（][0-9０-９]@[）)]", , , 1, , , , , , "\1（1）", 2
            End With

            '处理标题格式和自动编号
            For Each i In .Paragraphs
                With i.Range
                    '处理二级标题（一、）
                    If .Text Like "一、*" Then
                        .Style = wdStyleHeading2  '设置为二级标题样式
                        With .ParagraphFormat
                            .SpaceBefore = 0
                            .SpaceAfter = 0
                        End With
                        t2 = t2 + 1  '二级标题计数器加1
                        t3 = 0: t4 = 0: t5 = 0  '重置下级标题计数器
                        '先添加字段
                        doc.Range(Start:=.Start, End:=.Characters(InStr(.Text, "、")).Start).Select
                        Selection.Fields.Add Range:=Selection.Range, Text:="= " & t2 & " \* CHINESENUM3"
                        '更新字段
                        .Fields.Update
                        '设置整个段落的字体格式（包括标号和标题）
                        .Font.Color = wdColorRed
                        .Font.NameFarEast = "方正黑体_GBK"
                        .Font.NameAscii = "方正黑体_GBK"

                    '处理三级标题（（一））
                    ElseIf .Text Like "（一）*" Then
                        .Style = wdStyleHeading3  '设置为三级标题样式
                        With .ParagraphFormat
                            .SpaceBefore = 0
                            .SpaceAfter = 0
                        End With
                        t3 = t3 + 1  '三级标题计数器加1
                        t4 = 0: t5 = 0  '重置下级标题计数器
                        '先添加字段
                        doc.Range(Start:=.Start + 1, End:=.Characters(InStr(.Text, "）")).Start).Select
                        Selection.Fields.Add Range:=Selection.Range, Text:="= " & t3 & " \* CHINESENUM3"
                        '更新字段
                        .Fields.Update
                        '设置整个段落的字体格式（包括标号和标题）
                        .Font.Color = wdColorPink
                        .Font.NameFarEast = "方正仿宋_GBK"
                        .Font.NameAscii = "方正仿宋_GBK"

                    '处理四级标题（1．）
                    ElseIf .Text Like "#．*" Then
                        .Style = wdStyleHeading4  '设置为四级标题样式
                        With .ParagraphFormat
                            .SpaceBefore = 0
                            .SpaceAfter = 0
                        End With
                        .Font.Color = wdColorGreen  '设置字体颜色为绿色
                        .Font.NameFarEast = "方正仿宋_GBK"  '设置中文字体
                        .Font.NameAscii = "方正仿宋_GBK"  '设置英文字体
                        With .Font
                            .Size = 16  '设置字体大小为16磅
                        End With
'                        With .ParagraphFormat
'                            .SpaceBefore = 13  '段前间距13磅
'                            .SpaceAfter = 13  '段后间距13磅
'                        End With
                        t4 = t4 + 1  '四级标题计数器加1
                        t5 = 0  '重置五级标题计数器
                        '直接替换标号文本（会自动继承段落字体）
                        doc.Range(Start:=.Start, End:=.Characters(InStr(.Text, "．")).Start).Text = t4

                    '处理五级标题（（1））
                    ElseIf .Text Like "（#）*" Then
                        .Style = wdStyleHeading5  '设置为五级标题样式
                        .Font.Color = wdColorOrange  '设置字体颜色为橙色
                        .Font.NameFarEast = "方正仿宋_GBK"  '设置中文字体
                        .Font.NameAscii = "方正仿宋_GBK"  '设置英文字体
                        With .Font
                            .Size = 16  '设置字体大小为16磅
                        End With
                        'With .ParagraphFormat
                        '    .SpaceBefore = 13  '段前间距13磅
                        '    .SpaceAfter = 13  '段后间距13磅
                        'End With
                        t5 = t5 + 1  '五级标题计数器加1
                        '直接替换标号文本（会自动继承段落字体）
                        doc.Range(Start:=.Characters(1).End, End:=.Characters(InStr(.Text, "）")).Start).Text = t5

                    '删除只有回车符的空段落
                    ElseIf Asc(.Text) = 13 Then
                        .Delete

                    '遇到附件时重置所有标题计数器
                    ElseIf .Text Like "[!^13]附件*" Or .Text Like "附件*" Then
                        t2 = 0: t3 = 0: t4 = 0: t5 = 0
                    End If

                    '对所有标题样式进行统一格式设置
                    If .Style Like "标题*" Then
                        .Font.Kerning = 0  '设置字符间距为0
                        With .ParagraphFormat
                            .LineSpacingRule = wdLineSpaceExactly  '行距规则：固定值
                            .LineSpacing = 29  '行距：29磅
                            .CharacterUnitFirstLineIndent = 1.99  '首行缩进：1.99字符
                            .AutoAdjustRightIndent = False  '不自动调整右缩进
                            .DisableLineHeightGrid = True  '禁用行高网格
                            .KeepWithNext = False  '不与下一段保持在同一页
                            .KeepTogether = False  '不保持段落在同一页
                        End With

                        '处理带冒号的标题（如"标题：内容"）
                        If .Sentences(1) Like "*：??*" Then
                            .MoveStart 1, InStr(.Text, "：")  '移动到冒号后
                            With .Font
                                .NameFarEast = "方正仿宋_GBK"  '冒号后内容用仿宋字体
                                .NameAscii = "宋体"
                                .Bold = False  '不加粗
                                .Color = wdColorBlue  '设置为蓝色
                            End With

                            '根据标题级别处理标点符号
                            If .Paragraphs(1).Range.Style Like "标题*" & "[23]" Then
                                '二、三级标题：删除末尾标点
                                If .Text Like "*[。：；，、！？…—.:;,!?]?" Then
                                    .Characters.Last.Previous.Delete
                                End If
                            ElseIf .Paragraphs(1).Range.Style Like "标题*" & "[45]" Then
                                '四、五级标题：确保以句号结尾
                                If .Text Like "*[!。：；，、！？…—.:;,!?]?" Then
                                    If .Text Like "*[!0-9a-zA-Z]?" Then
                                        .Characters.Last.InsertBefore Text:="。"
                                    End If
                                End If
                            End If
                        Else
                            '处理不带冒号的标题
                            If .Sentences.Count = 1 Then
                                '单句标题：删除末尾标点
                                If .Text Like "*[。：；，、！？…—.:;,!?]?" Then .Characters.Last.Previous.Delete
                            Else
                                '多句标题：第一句后的内容用仿宋字体
                                With doc.Range(Start:=.Sentences(1).End, End:=.End).Font
                                    .NameFarEast = "方正仿宋_GBK"
                                    .NameAscii = "宋体"
                                    .Bold = False
                                    .Color = wdColorBlue
                                End With
                            End If
                        End If
                    End If
                End With
            Next

            '在区域开始处插入分隔段落
            If .Start <> 0 Then
                If Len(.Text) <> 0 Then
                    .InsertParagraphBefore
                    With .Paragraphs(1).Range
                        .Font.Size = 6  '设置很小的字体
                        With .ParagraphFormat
                            .SpaceBefore = 0  '段前间距为0
                            .SpaceAfter = 0  '段后间距为0
                        End With
                    End With
                End If
            End If
        End With
    Next

    '设置标题间距：除一级标题外，其他标题段前段后间距为0
    For Each i In doc.Paragraphs
        With i.Range
            On Error Resume Next  '忽略可能的错误
            Dim styleName As String
            styleName = .Style.NameLocal  '获取样式的本地名称
            '如果是二到五级标题，设置段前段后间距为0
            If (styleName = "标题2" Or styleName = "标题3" Or styleName = "标题4" Or styleName = "标题5") Then
                With .ParagraphFormat
                    .SpaceBefore = 0  '段前间距为0
                    .SpaceAfter = 0  '段后间距为0
                End With
            End If
            On Error GoTo 0  '恢复错误处理
        End With
    Next
End Function

'==============================================================================
' 函数：Title1
' 功能：设置一级标题格式
' 说明：处理文档标题、称呼、标题间距等
'==============================================================================
Function Title1()
'一级标题处理函数
    Dim doc As Document, i As Paragraph  '定义文档和段落变量

    Set doc = ActiveDocument  '获取当前文档引用

    '处理文档的第一个段落（通常是标题）
    With doc.Paragraphs(1).Range
        '扩展标题范围，包含多行标题
        If .End <> doc.Content.End Then
            '检查下一段是否应该包含在标题中
            If Not (.Next(4, 1) Like "*[。：；，、！？…—.:;,!?]?" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第[一1]*" Or .Next.Information(12)) Then .MoveEnd 4
        End If
        '再次检查是否需要继续扩展
        If .End <> doc.Content.End Then
            If Not (.Next(4, 1) Like "*[。：；，、！？…—.:;,!?]?" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第[一1]*" Or .Next.Information(12)) Then .MoveEnd 4
        End If
        '在标题末尾插入段落分隔
        If .End <> doc.Content.End Then
            '.Characters.Last.InsertParagraphBefore
        End If
        '在标题前插入段落
        '.InsertParagraphBefore
        
        '设置为一级标题样式
        .Style = wdStyleHeading1
        
        '设置一级标题的字体格式
        With .Font
            .NameFarEast = "方正小标宋_GBK"  '中文字体：方正小标宋
            .NameAscii = "宋体"  '英文字体：宋体
            .Kerning = 0  '字符间距：0
            .Size = 22  '字体大小：22磅（一级标题）
        End With
        
        '设置一级标题的段落格式
        With .ParagraphFormat
            .SpaceBeforeAuto = False  '不自动设置段前间距
            .SpaceAfterAuto = False  '不自动设置段后间距
            .SpaceBefore = 0  '先设置为0
            .SpaceAfter = 0  '先设置为0
            .LineSpacingRule = wdLineSpaceExactly  '行距规则：固定值
            .SpaceBefore = 35  '段前间距：35磅
            .SpaceAfter = 15   '段后间距：15磅
            .Alignment = wdAlignParagraphCenter  '居中对齐
            .AutoAdjustRightIndent = False  '不自动调整右缩进
            .DisableLineHeightGrid = True  '禁用行高网格
        End With

        '处理称呼段落
        If .End <> doc.Content.End Then
            With .Next(4, 1)
                If Not .Information(12) Then  '如果不是表格
                    If .Text Like "*[：:]?" Then  '如果包含冒号
                        If .ComputeStatistics(1) < 3 Then  '如果字数少于3个
                            .Characters.Last.Previous.Text = "："  '统一冒号格式
                            With .Find
                                .Execute "(", , , 0, , , , , , "（", 2  '替换括号
                                .Execute ")", , , 0, , , , , , "）", 2
                            End With
                            .Font.Color = wdColorViolet  '设置称呼颜色为紫色
                            With .ParagraphFormat
                                .CharacterUnitFirstLineIndent = 0  '首行缩进为0
                                .FirstLineIndent = CentimetersToPoints(0)  '首行缩进为0厘米
                            End With
                        End If
                    End If
                End If
            End With
        End If

        '清理标题中的空格和括号
        With .Find
            .Execute "(", , , 0, , , , , , "（", 2  '替换左括号
            .Execute ")", , , 0, , , , , , "）", 2  '替换右括号
            .Execute "[ 　^s^t]", , , 1, , , , , , "", 2  '删除所有空格
        End With

        '处理标题中的（草稿）等标记
        If .Paragraphs.Count > 1 Then
            With .Paragraphs.Last.Previous.Range
            If .Text Like "（*）?" Then  '如果是括号格式
                With .Font
                    .NameFarEast = "方正楷体_GBK"  '设置为楷体
                    .NameAscii = "宋体"
                    .Size = 18  '字体大小18磅
                    .Color = wdColorTeal  '颜色设置为青色
                End With
                .Paragraphs.IncreaseSpacing  '增加段落间距
                '根据字数添加适当的空格
                If Len(.Text) = 6 Then
                    .Characters(2).InsertAfter Text:=" "
                    .Characters(4).InsertAfter Text:=" "
                ElseIf Len(.Text) = 5 Then
                    .Characters(2).InsertAfter Text:=" "
                End If
                .Next.ParagraphFormat.Space1  '设置下一段的间距
            End If

            '为标题添加字符间空格，使其美观
            If Not .Text Like "*）*" Then  '如果不包含右括号
                If .Text Like "???" Then  '3个字符
                    .Characters(1).InsertAfter Text:="    "
                ElseIf .Text Like "????" Then  '4个字符
                    If .Text Like "协议书?" Then .Font.Size = 26  '协议书用更大字体
                    .Characters(1).InsertAfter Text:="   "
                    .Characters(5).InsertAfter Text:="   "
                ElseIf .Text Like "?????" Then  '5个字符
                    .Characters(1).InsertAfter Text:="  "
                    .Characters(4).InsertAfter Text:="  "
                    .Characters(7).InsertAfter Text:="  "
                ElseIf .Text Like "??????" Then  '6个字符
                    .Characters(1).InsertAfter Text:="  "
                    .Characters(4).InsertAfter Text:="  "
                    .Characters(7).InsertAfter Text:="  "
                    .Characters(10).InsertAfter Text:="  "
                ElseIf .Text Like "???????" Then  '7个字符
                    .Characters(1).InsertAfter Text:=" " & ChrW(160)
                    .Characters(4).InsertAfter Text:=" " & ChrW(160)
                    .Characters(7).InsertAfter Text:=" " & ChrW(160)
                    .Characters(10).InsertAfter Text:=" " & ChrW(160)
                    .Characters(13).InsertAfter Text:=" " & ChrW(160)
                End If
            End If
        End With
        End If

        '处理标题中的特殊字符格式
        For Each i In .Paragraphs
            With i.Range
                '在特定字符前添加空格
                If .Text Like "[!（]*[）”〉》]" Then .InsertBefore Text:=" "
                If .Text Like "[“（《〈]*[!）]" Then .ParagraphFormat.CharacterUnitLeftIndent = -0.5
            End With
        Next

        '如果标题后是表格，进行特殊处理
        If .Next.Information(12) Then
            .Characters.First.Delete  '删除第一个字符
            .Characters.Last.Delete  '删除最后一个字符
            .ParagraphFormat.Space15  '设置1.5倍行距
        End If
    End With
End Function

'==============================================================================
' 函数：Inscribe
' 功能：处理落款和附件
' 说明：格式化日期、单位名称、附件列表等
'==============================================================================
Function Inscribe()
'落款处理函数
    '定义变量
    Dim doc As Document, r As Range, arr, TextSize&, Base!, lenUnit&, k&

    Set doc = ActiveDocument  '获取当前文档引用

    '处理日期格式（如2022-12-09转换为2022年12月9日）
    Set r = doc.Content
    With r.Find
        .ClearFormatting  '清除查找格式
        .Text = "^13[0-9]{4}?[0-9]{1,2}?[0-9]{1,2}[^13^12]"  '查找日期模式
        .Forward = True  '向前查找
        .MatchWildcards = True  '使用通配符
        Do While .Execute  '循环查找所有匹配项
            With .Parent
                .MoveStart  '移动到开始位置
                .Characters(5).Text = "年"  '在第5个字符位置插入"年"
                .Characters.Last.InsertBefore Text:="日"  '在最后插入"日"
                '根据月份位置插入"月"
                If .Characters(7) Like "[0-9]" Then
                    .Characters(8).Text = "月"
                Else
                    .Characters(7).Text = "月"
                End If
                .Start = .End  '移动到下一个位置
            End With
        Loop
    End With

    '进一步处理已格式化的日期
    Set r = doc.Content
    With r.Find
        .ClearFormatting
        .Text = "^13[0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日[^13^12]"  '查找年月日格式
        .Forward = True
        .MatchWildcards = True
        Do While .Execute
            With .Parent
                .MoveStart
                '删除月份和日期前的0
                If .Text Like "*0?月*" Then .Characters(6).Delete
                If .Text Like "*0?日*" Then .Characters.Last.Previous.Previous.Previous.Delete
                '检查字体大小，如果不是16磅则退出
                If Not .Font.Size = 16 Then k = 1: Exit Do
                .Start = .End
            End With
        Loop
        If k = 0 Then Exit Function  '如果没有找到合适的日期则退出
    End With

    '设置日期的格式
    With r
        .Font.Color = wdColorPink  '日期颜色设置为粉色
        .Font.NameFarEast = "方正仿宋_GBK"  '中文字体
        .Font.NameAscii = "宋体"  '英文字体
        TextSize = .Font.Size  '获取字体大小

        '设置日期的段落格式
        With .ParagraphFormat
            .Alignment = wdAlignParagraphRight  '右对齐
            .CharacterUnitRightIndent = 4  '右缩进4个字符
            .LineSpacingRule = wdLineSpaceExactly  '固定行距
            .LineSpacing = 29  '行距29磅
            .SpaceBefore = 58  '段前间距58磅（空两行）
        End With

        '根据字体大小和日期长度计算基准位置
        If TextSize = 16 Then
            If .Text Like "*年?月?日?" Then
                Base = 18.22
            ElseIf .Text Like "*年?月??日?" Or .Text Like "*年??月?日?" Then
                Base = 17.97
            Else
                Base = 17.72
            End If
        Else
            If .Text Like "*年?月?日?" Then
                Base = 20.7
            ElseIf .Text Like "*年?月??日?" Or .Text Like "*年??月?日?" Then
                Base = 20.45
            Else
                Base = 20.2
            End If
        End If

        '处理单位名称（日期前的内容）
        With .Previous(4, 1)
            If .Text Like "*[!。：；，、！？…—.:;,!?]?" Then  '如果不以标点结尾
                .Font.Color = wdColorRed  '单位名称颜色设置为红色
                .Font.NameFarEast = "方正仿宋_GBK"
                .Font.NameAscii = "宋体"
                .InsertBefore Text:=vbCr & vbCr  '在前面插入两个换行
                .SetRange Start:=.Paragraphs.Last.Range.Start, End:=.Paragraphs.Last.Range.End
                lenUnit = Len(.Text) - 1  '计算单位名称长度

                '根据单位名称长度调整字符间距
                If lenUnit = 9 Then
                    .Font.Spacing = 1
                ElseIf lenUnit = 8 Then
                    .Font.Spacing = 2
                ElseIf lenUnit = 2 Then
                    .Characters(1).InsertAfter Text:="  "
                ElseIf lenUnit = 3 Then
                    .Characters(1).InsertAfter Text:=" "
                    .Characters(3).InsertAfter Text:=" "
                ElseIf lenUnit = 4 Then
                    .Font.Spacing = 3
                ElseIf lenUnit = 5 Then
                    .Font.Spacing = 1
                End If

                '根据字体大小设置缩进数组
                If TextSize = 16 Then
                    arr = Array(1.2, 1.6, 6.5, 4.15, 2.7, 3.35, 7.45, 6.45, 5, 5.5, 6.1, 6.6, 7.2, 7.7, 8.25, 9.25, 10.25, 11.25, 12.25, 13.25, 14.25, 15.25, 16.25, 17.25)
                Else
                    arr = Array(1.2, 1.7, 7.85, 4.85, 2.75, 3.35, 8.55, 7.15, 5.15, 5.65, 6.25, 6.75, 7.15, 7.75, 8.35, 8.4, 9.3, 10.45, 11.35, 12.45, 13.45, 14.45, 15.45, 16.45)
                End If
                '设置首行缩进
                .ParagraphFormat.CharacterUnitFirstLineIndent = Base - arr(lenUnit - 2)
                .ParagraphFormat.SpaceBefore = 58  '段前间距
                .ParagraphFormat.LineSpacingRule = wdLineSpaceExactly
                .ParagraphFormat.LineSpacing = 29

                '根据单位名称长度调整日期的右缩进
                With .Next(4, 1).ParagraphFormat
                    If lenUnit < 17 Then
                        '长度小于17时不调整
                    ElseIf lenUnit = 17 Then
                        .CharacterUnitRightIndent = 6.5
                    ElseIf lenUnit = 18 Then
                        .CharacterUnitRightIndent = 7
                    ElseIf lenUnit = 19 Then
                        .CharacterUnitRightIndent = 7.85
                    ElseIf lenUnit = 20 Then
                        .CharacterUnitRightIndent = 8.52
                    ElseIf lenUnit = 21 Then
                        .CharacterUnitRightIndent = 9.2
                    ElseIf lenUnit = 22 Then
                        .CharacterUnitRightIndent = 9.88
                    ElseIf lenUnit = 23 Then
                        .CharacterUnitRightIndent = 10.55
                    ElseIf lenUnit = 24 Then
                        .CharacterUnitRightIndent = 11.22
                    ElseIf lenUnit >= 25 Then
                        lenUnit = 25
                        .CharacterUnitRightIndent = 12
                    End If
                End With
            Else
                .InsertParagraphAfter  '如果以标点结尾，插入段落后退出
                Exit Function
            End If
        End With
    End With

    '如果文档中没有附件，退出函数
    If doc.Content Like "*" & vbCr & "附*" = False Then Exit Function

    '处理附件部分
    Dim DateRange As Range, myRange As Range, i As Paragraph, j&, n&, oBefore&, oAfter&, oTitle$
    
    '处理落款前的附件
    Set DateRange = r
    Set r = doc.Range(Start:=0, End:=DateRange.End)
    With r.Find
        .ClearFormatting
        .Text = "^13附件*^13"  '查找附件模式
        .Forward = True
        .MatchWildcards = True
        .Execute
        If .Found = True Then  '如果找到前附件
            With .Parent
                .MoveStart
                Do
                    .MoveEnd 4
                Loop Until .Text Like "*" & vbCr & vbCr  '扩展到双换行
                .MoveEnd 1, -1
                .InsertParagraphBefore
                .MoveStart
                oBefore = 1  '标记找到前附件
                Set myRange = r
            End With
        End If
    End With

    '处理落款后的附件
sc:
    Set r = doc.Range(Start:=DateRange.End - 1, End:=doc.Content.End)
    With r.Find
        .ClearFormatting
        .Text = "[^13^12]附件*^13"  '查找后附件模式
        .Forward = True
        .MatchWildcards = True
        Do While .Execute
            With .Parent
                '处理换行符和分页符
                If Asc(.Text) = 13 Then
                    .Characters(1).InsertAfter Text:=Chr(12)  '插入分页符
                ElseIf Asc(.Text) = 12 Then
                    .MoveStart 1, -1
                End If
                .MoveStart 1, 2

                '处理特殊格式的附件标题
                Do While .Next(4, 1) Like "#．*" & vbCr Or .Next(4, 1) Like "##．*" & vbCr
                    .MoveEnd 4
                    If .End = doc.Content.End Then
                        oTitle = .Text
                        .Delete
                        .Previous.Delete
                        .Delete
                        oAfter = 1
                        GoTo sk
                    End If
                Loop

                .MoveEnd 1, -1
                n = n + 1  '附件计数器
                .Text = "附件" & n & "："  '设置附件标号

                '设置附件标号的格式
                With .Font
                    .NameFarEast = "方正黑体_GBK"
                    .NameAscii = "宋体"
                    .Bold = False
                    .Color = wdColorRed
                End With
                With .ParagraphFormat
                    .CharacterUnitFirstLineIndent = 0
                    .FirstLineIndent = CentimetersToPoints(0)
                    .LineSpacingRule = wdLineSpaceExactly
                    .LineSpacing = 29
                End With

                '处理附件标题
                With .Next(4, 1)
                    If Not .Information(12) Then  '如果不是表格
                        '扩展标题范围
                        If Not (.Next.Information(12)) Then
                            If Not (.Next(4, 1) Like "*[。：:_]*" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第一*") Then
                                .MoveEnd 4
                                .Paragraphs(1).Range.Characters.Last.Delete
                            End If
                        End If
                        If Not (.Next.Information(12)) Then
                            If Not (.Next(4, 1) Like "*[。：:_]*" Or .Next(4, 1) Like "[一1][、.．]*" Or .Next(4, 1) Like "（[一1]）*" Or .Next(4, 1) Like "第一*") Then
                                .MoveEnd 4
                                .Paragraphs(1).Range.Characters.Last.Delete
                            End If
                        End If
                    Else
                        '如果是表格，进行特殊处理
                        .Next.Next.Select
                        Selection.SplitTable
                        Selection.Previous.Tables(1).Rows.ConvertToText Separator:=wdSeparateByParagraphs, NestedTables:=True
                        .Next(4, 1).Delete
                        .Expand 4
                    End If

                    '清理附件标题中的空格
                    .Find.Execute "[ 　^s^t]", , , 1, , , , , , "", 2

                    oTitle = oTitle & .Text  '累积附件标题
                    
                    '设置附件标题的格式
                    With .Font
                        .NameFarEast = "方正宋体_GBK"
                        .NameAscii = "宋体"
                        .Size = 20  '附件标题字体大小
                        .Bold = False
                        .Color = wdColorAutomatic
                    End With
                    With .ParagraphFormat
                        .CharacterUnitFirstLineIndent = 0
                        .FirstLineIndent = CentimetersToPoints(0)
                        .Alignment = wdAlignParagraphCenter  '居中对齐
                        .LineSpacingRule = wdLineSpaceExactly
                        .LineSpacing = 29
                    End With
                    
                    '如果是竖向页面，添加额外格式
                    If .Sections(1).PageSetup.Orientation = wdOrientPortrait Then
                        '.InsertParagraphBefore
                        .Characters.Last.InsertBefore Text:=vbCr
                        .ParagraphFormat.LineSpacing = LinesToPoints(1.25)
                    End If
                    .Paragraphs.Last.Range.ParagraphFormat.Space15
                    
                    '处理特殊字符格式
                    If .Text Like "*[）”〉》]" Then .InsertBefore Text:=" "
                    If .Text Like "[“（《〈]*[!）]" Then .ParagraphFormat.CharacterUnitLeftIndent = -0.5
                End With
                oAfter = 1  '标记找到后附件
                .Start = .End
            End With
        Loop
    End With

    '处理缺失的附件标识
    With r
        If oAfter = 0 And Len(.Text) > 1 Then
            If .Text Like vbCr & Chr(12) & "*" Then
                .Characters(2).InsertAfter Text:="附件：" & vbCr
            ElseIf .Text Like vbCr & "*" Then
                .Characters(1).InsertAfter Text:="附件：" & vbCr
            ElseIf .Characters(2).Information(12) Then
                .Characters(2).Select
                With Selection
                    .SplitTable
                    .TypeText Text:="附件："
                    With .Paragraphs(1).Range
                        .Font.Size = 16
                        .Font.NameFarEast = "方正仿宋_GBK"
                        .Font.NameAscii = "宋体"
                        With .ParagraphFormat
                            .LineSpacingRule = wdLineSpaceExactly
                            .LineSpacing = 29
                            .AutoAdjustRightIndent = False
                            .DisableLineHeightGrid = True
                        End With
                    End With
                End With
            End If
            GoTo sc
        End If
        If n = 1 Then .Previous.Previous.Delete
    End With
    If oBefore = 0 And oAfter = 0 Then Exit Function

    '处理前后附件不一致的情况
    If oBefore = 1 Then
        If oAfter = 1 Then
            With myRange
                If .Text Like "附件[：:]" & vbCr & "*" Then .Paragraphs(1).Range.Delete
                If .Paragraphs.Count = n Then
                    .Text = oTitle
                Else
                    '弹出对话框让用户选择以哪个附件列表为准
                    If MsgBox("<前附件> " & .Paragraphs.Count & " 个：" & vbCr & .Text & vbCr _
                        & "<后附件> " & n & " 个：" & vbCr & oTitle & vbCr & "* 落款前后附件个数不一致！请选择：" & vbCr _
                        & "[是(Y)] 以<前附件>为准！     [否(N)] 以<后附件>为准！", 4 + 16) = vbNo Then .Text = oTitle
                End If
            End With
        End If
    Else
sk:
        If oAfter = 1 Then
            With DateRange
                .MoveStart 4, -4
                .InsertBefore Text:=vbCr & oTitle
                .MoveStart
                .MoveEnd 4, -5
            End With
            Set myRange = DateRange
        End If
    End If

    '设置附件列表的缩进格式
    With myRange
        '设置附件列表的字体
        With .Font
            .Color = wdColorBrown
            .NameFarEast = "方正仿宋_GBK"
            .NameAscii = "宋体"
            .Bold = False
        End With
        '设置附件列表的段落格式
        With .ParagraphFormat
            .SpaceBefore = 0
            .SpaceAfter = 0
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        '清理附件名称中的多余字符
        For Each i In .Paragraphs
            With i.Range
                If .Text Like "附*" Then .Characters(1).Delete
                If .Text Like "件*" Then .Characters(1).Delete
                If .Text Like "表*" Then .Characters(1).Delete
                If .Text Like "[一二三四五六七八九十]*" Then .Characters(1).Delete
                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "《*" Then .Characters(1).Delete
                If .Text Like "*》" Then .Characters.Last.Previous.Delete

                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "#[：:.．、，]*" Or .Text Like "##[：:.．、，]*" Then .Characters(1).Delete
                If .Text Like "[：:.．、，]*" Then .Characters(1).Delete
            End With
        Next

        If oBefore = 1 And oAfter = 0 Then n = myRange.Paragraphs.Count

        '添加附件编号
        If n = 1 Then
            .InsertBefore Text:=vbTab  '单个附件只用制表符
        Else
            For Each i In .Paragraphs
                j = j + 1
                i.Range.InsertBefore Text:=j & "．" & vbTab  '多个附件用数字编号
            Next
        End If

        '设置附件列表的缩进
        With .ParagraphFormat
            .CharacterUnitLeftIndent = 7.68  '左缩进
            .CharacterUnitFirstLineIndent = -1.56  '首行缩进（负值表示悬挂缩进）
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        .InsertBefore Text:="附件："  '在前面插入"附件："

        '设置"附件："的格式
        With .Paragraphs(1).Range.ParagraphFormat
            .CharacterUnitLeftIndent = 3.05
            .CharacterUnitFirstLineIndent = -4.62
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        '如果只有一个附件，调整缩进
        If n = 1 Then .ParagraphFormat.CharacterUnitFirstLineIndent = -3.1
    End With
End Function

'==============================================================================
' 函数：PageNumGW
' 功能：设置公文页码
' 说明：在页脚添加"— 页码 —"格式的页码，左对齐
'==============================================================================
Function PageNumGW()
'公文页码设置函数
    Dim Rng As Range  '定义范围变量
    
    With ActiveDocument.Sections(1)  '处理文档的第一节
        '清除页眉内容，防止干扰
        .Headers(wdHeaderFooterPrimary).Range.Delete

        With .Footers(wdHeaderFooterPrimary)  '处理主页脚
            .Range.Delete  '清除现有页脚内容
            '只有多页文档才添加页码
            If .Parent.Parent.ComputeStatistics(wdStatisticPages) > 1 Then
                '使用Selection方法来确保正确插入页码
                .Range.Select  '选择页脚范围
                Selection.TypeText "— "  '输入左边的破折号和空格
                Selection.Fields.Add Range:=Selection.Range, Type:=wdFieldPage  '插入页码字段
                Selection.TypeText " —"  '输入右边的空格和破折号

                '设置整个页脚的格式
                With .Range
                    .Font.NameAscii = "宋体"  '设置字体为宋体
                    .Font.Size = 12  '字体大小：12磅（4号字体）
                    .ParagraphFormat.Alignment = wdAlignParagraphLeft  '左对齐
                    .ParagraphFormat.SpaceBefore = 0  '段前间距为0
                    .ParagraphFormat.SpaceAfter = 0  '段后间距为0
                End With

                '更新页码字段，确保正确显示
                .Range.Fields.Update
            End If
        End With
    End With
End Function

'==============================================================================
' 函数：Common
' 功能：公共部分调整和收尾工作
' 说明：设置页面显示、样式重置、恢复字体颜色等
'==============================================================================
Function Common()
'公共处理函数
    NumPages  '调整页面显示模式
    StyleReset   '重置公文样式定义
    Selection.HomeKey Unit:=wdStory  '将光标移到文档开始
    AutoColor  '恢复字体颜色为自动（黑色）
End Function

'==============================================================================
' 函数：NumPages
' 功能：根据页数调整页面显示模式
' 说明：自动调整页面显示的列数，便于查看
'==============================================================================
Function NumPages()
'页面显示调整函数
    Dim p&  '页数变量
    p = ActiveDocument.ComputeStatistics(wdStatisticPages)  '获取文档总页数
    
    With ActiveWindow.ActivePane.View.Zoom  '设置页面缩放显示
        If p < 99 Then  '如果页数少于99页
            '在当前列数和3列之间切换，或设置为页数
            If .PageColumns = p Then .PageColumns = 3 Else .PageColumns = p
        Else  '如果页数很多
            '在15列和3列之间切换
            If .PageColumns = 15 Then .PageColumns = 3 Else .PageColumns = 15
        End If
        .PageRows = 1  '设置为单行显示
    End With
End Function

'==============================================================================
' 函数：StyleReset
' 功能：重置和定义公文样式
' 说明：设置各级标题和正文的标准格式
'==============================================================================
Function StyleReset()
'样式重置函数
    With ActiveDocument
        '设置正文样式
        With .Styles(wdStyleNormal).Font
            .NameFarEast = "方正仿宋_GBK"  '中文字体：方正仿宋
            .NameAscii = "宋体"  '英文字体：宋体
            .Size = 16  '字体大小：16磅（3号字体）
        End With
        With .Styles(wdStyleNormal).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly  '行距规则：固定值
            .LineSpacing = 29  '行距：29磅
        End With

        '设置一级标题样式
        With .Styles(wdStyleHeading1).Font
            .NameFarEast = "方正黑体_GBK"  '中文字体：方正黑体
            .NameAscii = "宋体"  '英文字体：宋体
            .Size = 26  '字体大小：26磅
        End With
        With .Styles(wdStyleHeading1).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29  '一级标题行距：29磅
        End With

        '设置二级标题样式
        With .Styles(wdStyleHeading2).Font
            .NameFarEast = "方正楷体_GBK"  '中文字体：方正楷体
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading2).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        '设置三级标题样式
        With .Styles(wdStyleHeading3).Font
            .NameFarEast = "方正仿宋_GBK"  '中文字体：方正仿宋
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading3).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        '设置四级标题样式
        With .Styles(wdStyleHeading4).Font
            .NameFarEast = "方正仿宋_GBK"  '中文字体：方正仿宋
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading4).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With

        '设置五级标题样式
        With .Styles(wdStyleHeading5).Font
            .NameFarEast = "方正仿宋_GBK"  '中文字体：方正仿宋
            .NameAscii = "宋体"
            .Size = 16
        End With
        With .Styles(wdStyleHeading5).ParagraphFormat
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 29
        End With
    End With
End Function

'==============================================================================
' 函数：AutoColor
' 功能：恢复字体颜色为自动（黑色）
' 说明：将所有临时设置的彩色字体恢复为正常的黑色
'==============================================================================
Function AutoColor()
'字体颜色恢复函数
    ActiveDocument.Content.Font.Color = wdColorAutomatic  '将整个文档的字体颜色设置为自动（黑色）
End Function
