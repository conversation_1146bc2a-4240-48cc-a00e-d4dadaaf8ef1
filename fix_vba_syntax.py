#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# Read the VBA file
with open('VBA/grork代码_详细注释版.vba', 'r', encoding='utf-8') as f:
    content = f.read()

# Split into lines
lines = content.split('\n')

# Fix line 488 (index 487)
if len(lines) > 487:
    lines[487] = '                If InStr(.Text, "）") > 0 Or InStr(.Text, "〉") > 0 Or InStr(.Text, "》") > 0 Then .InsertBefore Text:=" "'

# Fix line 489 (index 488)  
if len(lines) > 488:
    lines[488] = '                If InStr(.Text, "（") > 0 Or InStr(.Text, "《") > 0 Or InStr(.Text, "〈") > 0 Then .ParagraphFormat.CharacterUnitLeftIndent = -0.5'

# Write back to file
with open('VBA/grork代码_详细注释版.vba', 'w', encoding='utf-8') as f:
    f.write('\n'.join(lines))

print("Fixed VBA syntax errors on lines 488 and 489")
